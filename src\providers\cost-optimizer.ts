import { EventEmitter } from 'events';
import Redis from 'ioredis';
import { ProviderConfig, RequestContext } from './types';
import { logger } from '../utils/logger';

export class CostOptimizer extends EventEmitter {
  private redis: Redis;

  constructor(redis: Redis) {
    super();
    this.redis = redis;
  }

  async registerProvider(config: ProviderConfig): Promise<void> {
    await this.redis.hset(
      `costs:${config.id}`,
      {
        dailySpend: 0,
        monthlySpend: 0,
        averageCostPerRequest: 0,
        costTrend: 0,
        lastReset: new Date().toISOString()
      }
    );
  }

  async estimateCost(provider: ProviderConfig, context: RequestContext): Promise<number> {
    const model = provider.models[0]; // Simplified - should select appropriate model
    const estimatedTokens = this.estimateTokens(context);
    
    const inputCost = (estimatedTokens.input / 1000) * model.pricing.input;
    const outputCost = (estimatedTokens.output / 1000) * model.pricing.output;
    
    return inputCost + outputCost;
  }

  private estimateTokens(context: RequestContext): { input: number; output: number } {
    // Simplified token estimation
    const baseInput = 100;
    const baseOutput = 50;
    
    return {
      input: baseInput,
      output: baseOutput
    };
  }

  async recordUsage(provider: ProviderConfig, context: RequestContext, response: any): Promise<void> {
    const cost = response.cost || 0;
    const providerId = provider.id;
    
    // Update daily spend
    const today = new Date().toISOString().split('T')[0];
    const dailyKey = `costs:daily:${providerId}:${today}`;
    await this.redis.incrbyfloat(dailyKey, cost);
    await this.redis.expire(dailyKey, 86400 * 30); // Keep for 30 days

    // Update monthly spend
    const month = new Date().toISOString().slice(0, 7);
    const monthlyKey = `costs:monthly:${providerId}:${month}`;
    await this.redis.incrbyfloat(monthlyKey, cost);
    await this.redis.expire(monthlyKey, 86400 * 365); // Keep for 1 year

    // Update provider totals
    const totals = await this.redis.hgetall(`costs:${providerId}`);
    const dailySpend = parseFloat(totals.dailySpend || '0') + cost;
    const monthlySpend = parseFloat(totals.monthlySpend || '0') + cost;
    const totalRequests = parseInt(totals.totalRequests || '0') + 1;
    
    const averageCostPerRequest = monthlySpend / totalRequests;
    
    await this.redis.hset(
      `costs:${providerId}`,
      {
        dailySpend: dailySpend.toFixed(6),
        monthlySpend: monthlySpend.toFixed(6),
        averageCostPerRequest: averageCostPerRequest.toFixed(6),
        totalRequests: totalRequests.toString(),
        lastUpdate: new Date().toISOString()
      }
    );

    // Check cost thresholds
    await this.checkCostThresholds(providerId, dailySpend, monthlySpend);
  }

  private async checkCostThresholds(providerId: string, dailySpend: number, monthlySpend: number): Promise<void> {
    const thresholds = await this.redis.hgetall(`thresholds:${providerId}`);
    
    const dailyLimit = parseFloat(thresholds.daily || '100');
    const monthlyLimit = parseFloat(thresholds.monthly || '1000');

    if (dailySpend >= dailyLimit) {
      this.emit('cost:daily:threshold:exceeded', { providerId, dailySpend, limit: dailyLimit });
    }

    if (monthlySpend >= monthlyLimit) {
      this.emit('cost:monthly:threshold:exceeded', { providerId, monthlySpend, limit: monthlyLimit });
    }
  }

  async getCostMetrics(providerId: string): Promise<{
    dailySpend: number;
    monthlySpend: number;
    averageCostPerRequest: number;
    totalRequests: number;
  }> {
    const data = await this.redis.hgetall(`costs:${providerId}`);
    
    return {
      dailySpend: parseFloat(data.dailySpend || '0'),
      monthlySpend: parseFloat(data.monthlySpend || '0'),
      averageCostPerRequest: parseFloat(data.averageCostPerRequest || '0'),
      totalRequests: parseInt(data.totalRequests || '0')
    };
  }

  async setCostThresholds(providerId: string, thresholds: {
    daily?: number;
    monthly?: number;
  }): Promise<void> {
    await this.redis.hset(`thresholds:${providerId}`, thresholds);
  }

  async resetDailySpend(providerId: string): Promise<void> {
    await this.redis.hset(`costs:${providerId}`, { dailySpend: 0 });
  }

  async resetMonthlySpend(providerId: string): Promise<void> {
    await this.redis.hset(`costs:${providerId}`, { monthlySpend: 0 });
  }
}