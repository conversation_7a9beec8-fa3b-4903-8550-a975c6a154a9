export enum ProviderType {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GOOGLE = 'google',
  MISTRAL = 'mistral',
  GROQ = 'groq',
  CUSTOM = 'custom'
}

export interface ProviderConfig {
  id: string;
  name: string;
  type: ProviderType;
  apiKey: string;
  baseUrl?: string;
  models: ModelConfig[];
  capabilities: ProviderCapability[];
  rateLimits: RateLimitConfig;
  pricing: PricingConfig;
  health: HealthConfig;
}

export interface ModelConfig {
  id: string;
  name: string;
  contextWindow: number;
  maxTokens: number;
  supportsStreaming: boolean;
  supportsTools: boolean;
  supportsImages: boolean;
  pricing: ModelPricing;
}

export interface ProviderCapability {
  type: string;
  features: string[];
  constraints: Record<string, any>;
}

export interface RateLimitConfig {
  requestsPerMinute: number;
  tokensPerMinute: number;
  requestsPerDay?: number;
  burstLimit?: number;
}

export interface PricingConfig {
  inputCostPer1K: number;
  outputCostPer1K: number;
  currency: string;
  billingGranularity: 'token' | 'request';
}

export interface HealthConfig {
  timeout: number;
  retryAttempts: number;
  circuitBreakerThreshold: number;
  healthCheckInterval: number;
}

export interface ModelPricing {
  input: number;
  output: number;
  training?: number;
}

export interface ProviderState {
  providerId: string;
  status: ProviderStatus;
  healthScore: number;
  lastHealthCheck: Date;
  currentLoad: LoadMetrics;
  recentErrors: ProviderError[];
  costMetrics: CostMetrics;
}

export enum ProviderStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  OFFLINE = 'offline'
}

export interface LoadMetrics {
  requestsPerMinute: number;
  tokensPerMinute: number;
  queueLength: number;
  averageResponseTime: number;
}

export interface ProviderError {
  timestamp: Date;
  error: string;
  type: ErrorType;
  count: number;
}

export enum ErrorType {
  TIMEOUT = 'timeout',
  RATE_LIMIT = 'rate_limit',
  AUTH_ERROR = 'auth_error',
  API_ERROR = 'api_error',
  NETWORK_ERROR = 'network_error'
}

export interface CostMetrics {
  dailySpend: number;
  monthlySpend: number;
  averageCostPerRequest: number;
  costTrend: number;
}

export interface SelectionStrategy {
  type: SelectionType;
  weights: SelectionWeights;
  constraints: SelectionConstraints;
}

export enum SelectionType {
  COST_OPTIMIZED = 'cost_optimized',
  PERFORMANCE_OPTIMIZED = 'performance_optimized',
  BALANCED = 'balanced',
  QUALITY_PRIORITY = 'quality_priority',
  CUSTOM = 'custom'
}

export interface SelectionWeights {
  cost: number;
  performance: number;
  quality: number;
  reliability: number;
}

export interface SelectionConstraints {
  maxCost?: number;
  minQuality?: number;
  requiredCapabilities?: string[];
  excludedProviders?: string[];
  region?: string;
}

export interface RequestContext {
  id: string;
  userId: string;
  organizationId: string;
  sessionId?: string;
  modelRequirements: ModelRequirements;
  priority: RequestPriority;
  metadata: Record<string, any>;
}

export interface ModelRequirements {
  taskType: string;
  minContextWindow?: number;
  requiredCapabilities?: string[];
  preferredProviders?: string[];
  maxCost?: number;
  streaming?: boolean;
}

export enum RequestPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4
}

export interface ProviderSelection {
  provider: ProviderConfig;
  model: ModelConfig;
  estimatedCost: number;
  confidence: number;
}

export interface AIRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  tools?: any[];
  stream?: boolean;
}

export interface AIResponse {
  id: string;
  provider: string;
  model: string;
  choices: Array<{
    message: {
      role: string;
      content: string;
      tool_calls?: any[];
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  cost: number;
  timestamp: Date;
}