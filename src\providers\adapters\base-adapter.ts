import { ProviderConfig, ModelConfig, AIRequest, AIResponse } from '../types';

export interface ProviderAdapter {
  execute(request: AIRequest, selection: { provider: ProviderConfig; model: ModelConfig }): Promise<AIResponse>;
  validateConfig(config: ProviderConfig): boolean;
  getModels(config: ProviderConfig): ModelConfig[];
}

export abstract class BaseAdapter implements ProviderAdapter {
  abstract execute(request: AIRequest, selection: { provider: ProviderConfig; model: ModelConfig }): Promise<AIResponse>;
  abstract validateConfig(config: ProviderConfig): boolean;
  abstract getModels(config: ProviderConfig): ModelConfig[];
  
  protected calculateCost(tokens: { input: number; output: number }, pricing: any): number {
    const inputCost = (tokens.input / 1000) * pricing.input;
    const outputCost = (tokens.output / 1000) * pricing.output;
    return inputCost + outputCost;
  }
}