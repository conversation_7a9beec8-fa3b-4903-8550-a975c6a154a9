import { EventEmitter } from 'events';
import Redis from 'ioredis';
import { ProviderOrchestrator } from '../providers/orchestrator';
import { ProviderConfig, AIRequest, AIResponse, RequestContext, SelectionStrategy } from '../providers/types';
import { logger } from '../utils/logger';

export interface SDKConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  retries?: number;
  provider?: string;
  environment?: 'development' | 'staging' | 'production';
}

export interface RequestOptions {
  provider?: string;
  model?: string;
  strategy?: SelectionStrategy;
  priority?: number;
  metadata?: Record<string, any>;
}

export class UniversalClient extends EventEmitter {
  private orchestrator: ProviderOrchestrator;
  private redis: Redis;
  private config: SDKConfig;

  constructor(config: SDKConfig) {
    super();
    this.config = config;
    this.redis = new Redis();
    this.orchestrator = new ProviderOrchestrator(this.redis);
    
    this.setupEventHandlers();
  }

  async registerProvider(config: ProviderConfig): Promise<void> {
    await this.orchestrator.registerProvider(config);
  }

  async chat(request: AIRequest, options: RequestOptions = {}): Promise<AIResponse> {
    const context: RequestContext = {
      id: this.generateRequestId(),
      userId: this.config.apiKey,
      organizationId: 'default',
      modelRequirements: {
        taskType: 'chat',
        streaming: request.stream,
        maxCost: 0.01
      },
      priority: options.priority || 2,
      metadata: options.metadata || {}
    };

    try {
      const response = await this.orchestrator.executeRequest(context, request);
      return response;
    } catch (error) {
      logger.error('Chat request failed:', error);
      throw error;
    }
  }

  async stream(request: AIRequest, options: RequestOptions = {}): Promise<AsyncIterable<AIResponse>> {
    const context: RequestContext = {
      id: this.generateRequestId(),
      userId: this.config.apiKey,
      organizationId: 'default',
      modelRequirements: {
        taskType: 'chat',
        streaming: true,
        maxCost: 0.01
      },
      priority: options.priority || 2,
      metadata: options.metadata || {}
    };

    // Implementation for streaming
    return this.createStreamingResponse(context, request);
  }

  async getProviders(): Promise<ProviderConfig[]> {
    const registry = this.orchestrator['providerRegistry'];
    return await registry.getAll();
  }

  async getProviderStatus(providerId: string): Promise<any> {
    const healthMonitor = this.orchestrator['healthMonitor'];
    return await healthMonitor.getState(providerId);
  }

  async getCostMetrics(providerId: string): Promise<any> {
    const costOptimizer = this.orchestrator['costOptimizer'];
    return await costOptimizer.getCostMetrics(providerId);
  }

  private setupEventHandlers(): void {
    this.orchestrator.on('provider:registered', (config) => {
      this.emit('provider:registered', config);
    });

    this.orchestrator.on('provider:health:changed', (data) => {
      this.emit('provider:health:changed', data);
    });
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async *createStreamingResponse(context: RequestContext, request: AIRequest): AsyncIterable<AIResponse> {
    // Placeholder for streaming implementation
    yield {
      id: context.id,
      provider: 'mock',
      model: 'mock-model',
      choices: [{
        message: {
          role: 'assistant',
          content: 'Streaming not implemented yet'
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: 10,
        completion_tokens: 5,
        total_tokens: 15
      },
      cost: 0.001,
      timestamp: new Date()
    };
  }

  async shutdown(): Promise<void> {
    await this.redis.quit();
  }
}

// Factory function for creating clients
export function createClient(config: SDKConfig): UniversalClient {
  return new UniversalClient(config);
}

// REST API client wrapper
export class RESTClient {
  private baseUrl: string;
  private apiKey: string;

  constructor(baseUrl: string, apiKey: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  async chat(request: AIRequest, options: RequestOptions = {}): Promise<AIResponse> {
    const response = await fetch(`${this.baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({ request, options })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  async stream(request: AIRequest, options: RequestOptions = {}): Promise<ReadableStream> {
    const response = await fetch(`${this.baseUrl}/api/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({ request, options })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.body!;
  }
