import Redis from 'ioredis';
import { ProviderConfig, RequestContext } from './types';
import { logger } from '../utils/logger';

export class RateLimiter {
  private redis: Redis;

  constructor(redis: Redis) {
    this.redis = redis;
  }

  async registerProvider(config: ProviderConfig): Promise<void> {
    await this.redis.hset(
      `rate_limits:${config.id}`,
      {
        requestsPerMinute: config.rateLimits.requestsPerMinute,
        tokensPerMinute: config.rateLimits.tokensPerMinute,
        requestsPerDay: config.rateLimits.requestsPerDay || 0,
        burstLimit: config.rateLimits.burstLimit || 0
      }
    );
  }

  async checkLimit(providerId: string, context: RequestContext): Promise<boolean> {
    const limits = await this.redis.hgetall(`rate_limits:${providerId}`);
    
    const requestsPerMinute = parseInt(limits.requestsPerMinute || '60');
    const tokensPerMinute = parseInt(limits.tokensPerMinute || '10000');
    
    const minuteKey = this.getMinuteKey(providerId);
    const dayKey = this.getDayKey(providerId);
    
    const [currentRequests, currentTokens] = await Promise.all([
      this.redis.get(minuteKey),
      this.redis.get(`${minuteKey}:tokens`)
    ]);
    
    const requests = parseInt(currentRequests || '0');
    const tokens = parseInt(currentTokens || '0');
    
    if (requests >= requestsPerMinute || tokens >= tokensPerMinute) {
      return false;
    }
    
    return true;
  }

  async recordUsage(providerId: string, tokens: number): Promise<void> {
    const minuteKey = this.getMinuteKey(providerId);
    const dayKey = this.getDayKey(providerId);
    
    const pipeline = this.redis.pipeline();
    
    // Increment request count
    pipeline.incr(minuteKey);
    pipeline.expire(minuteKey, 60);
    
    // Increment token count
    pipeline.incrby(`${minuteKey}:tokens`, tokens);
    pipeline.expire(`${minuteKey}:tokens`, 60);
    
    // Increment daily counts
    pipeline.incr(dayKey);
    pipeline.incrby(`${dayKey}:tokens`, tokens);
    pipeline.expire(dayKey, 86400);
    
    await pipeline.exec();
  }

  async getUsage(providerId: string): Promise<{
    requestsPerMinute: number;
    tokensPerMinute: number;
    requestsPerDay: number;
    tokensPerDay: number;
  }> {
    const minuteKey = this.getMinuteKey(providerId);
    const dayKey = this.getDayKey(providerId);
    
    const [minRequests, minTokens, dayRequests, dayTokens] = await Promise.all([
      this.redis.get(minuteKey),
      this.redis.get(`${minuteKey}:tokens`),
      this.redis.get(dayKey),
      this.redis.get(`${dayKey}:tokens`)
    ]);
    
    return {
      requestsPerMinute: parseInt(minRequests || '0'),
      tokensPerMinute: parseInt(minTokens || '0'),
      requestsPerDay: parseInt(dayRequests || '0'),
      tokensPerDay: parseInt(dayTokens || '0')
    };
  }

  private getMinuteKey(providerId: string): string {
    const minute = Math.floor(Date.now() / 60000);
    return `rate:${providerId}:${minute}`;
  }

  private getDayKey(providerId: string): string {
    const day = new Date().toISOString().split('T')[0];
    return `rate:${providerId}:day:${day}`;
  }
}