import { EventEmitter } from 'events';
import Redis from 'ioredis';
import { ProviderConfig, ProviderState, ProviderStatus, ErrorType } from './types';
import { logger } from '../utils/logger';

export class HealthMonitor extends EventEmitter {
  private redis: Redis;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor(redis: Redis) {
    super();
    this.redis = redis;
    this.startHealthChecks();
  }

  async registerProvider(config: ProviderConfig): Promise<void> {
    const state: ProviderState = {
      providerId: config.id,
      status: ProviderStatus.HEALTHY,
      healthScore: 100,
      lastHealthCheck: new Date(),
      currentLoad: {
        requestsPerMinute: 0,
        tokensPerMinute: 0,
        queueLength: 0,
        averageResponseTime: 0
      },
      recentErrors: [],
      costMetrics: {
        dailySpend: 0,
        monthlySpend: 0,
        averageCostPerRequest: 0,
        costTrend: 0
      }
    };

    await this.redis.hset(
      `health:${config.id}`,
      {
        ...state,
        lastHealthCheck: state.lastHealthCheck.toISOString(),
        recentErrors: JSON.stringify(state.recentErrors)
      }
    );
  }

  async getState(providerId: string): Promise<ProviderState> {
    const data = await this.redis.hgetall(`health:${providerId}`);
    
    if (!data || Object.keys(data).length === 0) {
      throw new Error(`Provider ${providerId} not found`);
    }

    return {
      providerId: data.providerId,
      status: data.status as ProviderStatus,
      healthScore: parseFloat(data.healthScore),
      lastHealthCheck: new Date(data.lastHealthCheck),
      currentLoad: JSON.parse(data.currentLoad || '{}'),
      recentErrors: JSON.parse(data.recentErrors || '[]'),
      costMetrics: JSON.parse(data.costMetrics || '{}')
    };
  }

  async getAllStates(): Promise<ProviderState[]> {
    const keys = await this.redis.keys('health:*');
    const states = await Promise.all(
      keys.map(key => this.getState(key.replace('health:', '')))
    );
    
    return states;
  }

  async recordSuccess(providerId: string): Promise<void> {
    const state = await this.getState(providerId);
    state.healthScore = Math.min(100, state.healthScore + 1);
    state.lastHealthCheck = new Date();
    
    await this.updateState(providerId, state);
  }

  async recordError(providerId: string, error: any): Promise<void> {
    const state = await this.getState(providerId);
    
    state.recentErrors.push({
      timestamp: new Date(),
      error: error.message || error.toString(),
      type: this.classifyError(error),
      count: 1
    });

    // Keep only last 10 errors
    state.recentErrors = state.recentErrors.slice(-10);
    
    // Update health score
    state.healthScore = Math.max(0, state.healthScore - 5);
    
    // Update status based on health score
    if (state.healthScore >= 80) {
      state.status = ProviderStatus.HEALTHY;
    } else if (state.healthScore >= 50) {
      state.status = ProviderStatus.DEGRADED;
    } else if (state.healthScore >= 20) {
      state.status = ProviderStatus.UNHEALTHY;
    } else {
      state.status = ProviderStatus.OFFLINE;
    }

    await this.updateState(providerId, state);
    
    this.emit('provider:health:changed', {
      providerId,
      status: state.status,
      healthScore: state.healthScore
    });
  }

  private classifyError(error: any): ErrorType {
    const message = error.message || error.toString().toLowerCase();
    
    if (message.includes('timeout')) return ErrorType.TIMEOUT;
    if (message.includes('rate limit')) return ErrorType.RATE_LIMIT;
    if (message.includes('authentication') || message.includes('api key')) return ErrorType.AUTH_ERROR;
    if (message.includes('network') || message.includes('connection')) return ErrorType.NETWORK_ERROR;
    
    return ErrorType.API_ERROR;
  }

  private async updateState(providerId: string, state: ProviderState): Promise<void> {
    await this.redis.hset(
      `health:${providerId}`,
      {
        ...state,
        lastHealthCheck: state.lastHealthCheck.toISOString(),
        recentErrors: JSON.stringify(state.recentErrors)
      }
    );
  }

  private startHealthChecks(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const providers = await this.redis.smembers('providers:all');
        
        for (const providerId of providers) {
          await this.performHealthCheck(providerId);
        }
      } catch (error) {
        logger.error('Health check failed:', error);
      }
    }, 30000); // Check every 30 seconds
  }

  private async performHealthCheck(providerId: string): Promise<void> {
    try {
      // Implement actual health check logic here
      // For now, just update the last check time
      const state = await this.getState(providerId);
      state.lastHealthCheck = new Date();
      await this.updateState(providerId, state);
    } catch (error) {
      logger.error(`Health check failed for ${providerId}:`, error);
      await this.recordError(providerId, error);
    }
  }

  async shutdown(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
  }
}