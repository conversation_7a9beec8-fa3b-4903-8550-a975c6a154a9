import { EventEmitter } from 'events';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { ProviderConfig, ProviderState, ProviderSelection, RequestContext, SelectionStrategy } from './types';
import { HealthMonitor } from './health-monitor';
import { CostOptimizer } from './cost-optimizer';
import { RateLimiter } from './rate-limiter';
import { CircuitBreaker } from './circuit-breaker';
import { ProviderRegistry } from './provider-registry';
import { ProviderAdapter } from './adapters/base-adapter';

export class ProviderOrchestrator extends EventEmitter {
  private redis: Redis;
  private healthMonitor: HealthMonitor;
  private costOptimizer: CostOptimizer;
  private rateLimiter: RateLimiter;
  private circuitBreaker: CircuitBreaker;
  private providerRegistry: ProviderRegistry;
  private adapters: Map<string, ProviderAdapter> = new Map();

  constructor(redisUrl: string) {
    super();
    this.redis = new Redis(redisUrl);
    this.healthMonitor = new HealthMonitor(this.redis);
    this.costOptimizer = new CostOptimizer(this.redis);
    this.rateLimiter = new RateLimiter(this.redis);
    this.circuitBreaker = new CircuitBreaker(this.redis);
    this.providerRegistry = new ProviderRegistry(this.redis);
    
    this.initializeEventHandlers();
  }

  private initializeEventHandlers(): void {
    this.healthMonitor.on('provider:health:changed', (data) => {
      this.emit('provider:health:changed', data);
    });

    this.costOptimizer.on('cost:threshold:exceeded', (data) => {
      this.emit('cost:threshold:exceeded', data);
    });

    this.circuitBreaker.on('circuit:open', (data) => {
      this.emit('circuit:open', data);
    });
  }

  async registerProvider(config: ProviderConfig): Promise<void> {
    await this.providerRegistry.register(config);
    await this.healthMonitor.registerProvider(config);
    await this.costOptimizer.registerProvider(config);
    await this.rateLimiter.registerProvider(config);
    await this.circuitBreaker.registerProvider(config);
    
    this.emit('provider:registered', config);
    logger.info(`Provider registered: ${config.id}`);
  }

  async selectProvider(context: RequestContext, strategy?: SelectionStrategy): Promise<ProviderSelection> {
    const eligibleProviders = await this.getEligibleProviders(context, strategy);
    
    if (eligibleProviders.length === 0) {
      throw new Error('No eligible providers available');
    }

    const scoredProviders = await this.scoreProviders(eligibleProviders, context, strategy);
    const selected = await this.selectBestProvider(scoredProviders);
    
    await this.recordSelection(context, selected);
    
    return selected;
  }

  private async getEligibleProviders(
    context: RequestContext,
    strategy?: SelectionStrategy
  ): Promise<ProviderConfig[]> {
    const providers = await this.providerRegistry.getAll();
    const states = await this.healthMonitor.getAllStates();
    
    return providers.filter(provider => {
      const state = states.find(s => s.providerId === provider.id);
      
      if (!state || state.status === 'offline') return false;
      
      // Check capabilities
      if (context.modelRequirements?.requiredCapabilities) {
        const hasCapabilities = context.modelRequirements.requiredCapabilities.every(
          cap => provider.capabilities.some(c => c.type === cap)
        );
        if (!hasCapabilities) return false;
      }
      
      // Check constraints
      if (strategy?.constraints?.excludedProviders?.includes(provider.id)) return false;
      
      return true;
    });
  }

  private async scoreProviders(
    providers: ProviderConfig[],
    context: RequestContext,
    strategy?: SelectionStrategy
  ): Promise<any[]> {
    const states = await this.healthMonitor.getAllStates();
    
    return Promise.all(
      providers.map(async provider => {
        const state = states.find(s => s.providerId === provider.id)!;
        
        const [costScore, performanceScore, qualityScore, reliabilityScore] = await Promise.all([
          this.calculateCostScore(provider, context),
          this.calculatePerformanceScore(state),
          this.calculateQualityScore(provider),
          this.calculateReliabilityScore(state)
        ]);
        
        const weights = strategy?.weights || { cost: 0.3, performance: 0.3, quality: 0.2, reliability: 0.2 };
        const weightedScore = (
          costScore * weights.cost +
          performanceScore * weights.performance +
          qualityScore * weights.quality +
          reliabilityScore * weights.reliability
        );
        
        return {
          provider,
          state,
          scores: { cost: costScore, performance: performanceScore, quality: qualityScore, reliability: reliabilityScore },
          weightedScore
        };
      })
    );
  }

  private async calculateCostScore(provider: ProviderConfig, context: RequestContext): Promise<number> {
    const estimatedCost = await this.costOptimizer.estimateCost(provider, context);
    const maxBudget = context.modelRequirements?.maxCost || 0.01;
    return Math.max(0, 1 - (estimatedCost / maxBudget));
  }

  private async calculatePerformanceScore(state: ProviderState): Promise<number> {
    const responseTimeScore = Math.max(0, 1 - (state.currentLoad.averageResponseTime / 5000));
    const loadScore = Math.max(0, 1 - (state.currentLoad.queueLength / 100));
    return (responseTimeScore + loadScore) / 2;
  }

  private calculateQualityScore(provider: ProviderConfig): number {
    const modelQuality = provider.models.reduce((sum, model) => {
      return sum + (model.supportsTools ? 0.3 : 0) + (model.supportsStreaming ? 0.2 : 0);
    }, 0) / provider.models.length;
    
    return Math.min(1, modelQuality);
  }

  private calculateReliabilityScore(state: ProviderState): number {
    const errorRate = state.recentErrors.length / 100;
    const healthScore = state.healthScore / 100;
    return Math.max(0, healthScore - errorRate);
  }

  private async selectBestProvider(scored: any[]): Promise<ProviderSelection> {
    scored.sort((a, b) => b.weightedScore - a.weightedScore);
    const best = scored[0];
    
    return {
      provider: best.provider,
      model: this.selectBestModel(best.provider, best.scores),
      estimatedCost: await this.costOptimizer.estimateCost(best.provider, {}),
      confidence: best.weightedScore
    };
  }

  private selectBestModel(provider: ProviderConfig, scores: any): any {
    // Select model based on requirements and scores
    return provider.models[0];
  }

  private async recordSelection(context: RequestContext, selection: ProviderSelection): Promise<void> {
    await this.redis.hset(
      `selections:${context.id}`,
      {
        providerId: selection.provider.id,
        modelId: selection.model.id,
        estimatedCost: selection.estimatedCost,
        timestamp: new Date().toISOString()
      }
    );
  }

  async executeRequest(
    context: RequestContext,
    request: any,
    provider?: ProviderSelection
  ): Promise<any> {
    const selectedProvider = provider || await this.selectProvider(context);
    
    // Check rate limits
    const canProceed = await this.rateLimiter.checkLimit(selectedProvider.provider.id, context);
    if (!canProceed) {
      throw new Error('Rate limit exceeded');
    }

    // Check circuit breaker
    const circuitStatus = await this.circuitBreaker.getStatus(selectedProvider.provider.id);
    if (circuitStatus === 'OPEN') {
      throw new Error('Circuit breaker open');
    }

    try {
      const adapter = this.adapters.get(selectedProvider.provider.type);
      if (!adapter) {
        throw new Error(`No adapter found for provider type: ${selectedProvider.provider.type}`);
      }

      const result = await adapter.execute(request, selectedProvider);
      
      await this.updateMetrics(selectedProvider, context, result);
      
      return result;
    } catch (error) {
      await this.handleError(selectedProvider, error);
      throw error;
    }
  }

  private async updateMetrics(
    selection: ProviderSelection,
    context: RequestContext,
    result: any
  ): Promise<void> {
    await this.costOptimizer.recordUsage(selection.provider, context, result);
    await this.healthMonitor.recordSuccess(selection.provider.id);
  }

  private async handleError(selection: ProviderSelection, error: any): Promise<void> {
    await this.healthMonitor.recordError(selection.provider.id, error);
    await this.circuitBreaker.recordError(selection.provider.id, error);
  }

  async getProviderStatus(providerId: string): Promise<ProviderState> {
    return await this.healthMonitor.getState(providerId);
  }

  async getAllProviders(): Promise<ProviderConfig[]> {
    return await this.providerRegistry.getAll();
  }

  async shutdown(): Promise<void> {
    await this.redis.quit();
    this.removeAllListeners();
  }
}