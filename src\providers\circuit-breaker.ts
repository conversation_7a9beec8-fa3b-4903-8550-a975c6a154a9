import { EventEmitter } from 'events';
import Redis from 'ioredis';
import { logger } from '../utils/logger';

export type CircuitState = 'CLOSED' | 'OPEN' | 'HALF_OPEN';

export class CircuitBreaker extends EventEmitter {
  private redis: Redis;
  private defaultThreshold = 5;
  private defaultTimeout = 60000; // 1 minute

  constructor(redis: Redis) {
    super();
    this.redis = redis;
  }

  async registerProvider(config: any): Promise<void> {
    await this.redis.hset(
      `circuit:${config.id}`,
      {
        state: 'CLOSED',
        failureCount: 0,
        lastFailureTime: 0,
        threshold: config.health?.circuitBreakerThreshold || this.defaultThreshold,
        timeout: this.defaultTimeout
      }
    );
  }

  async getStatus(providerId: string): Promise<CircuitState> {
    const data = await this.redis.hgetall(`circuit:${providerId}`);
    return (data.state || 'CLOSED') as CircuitState;
  }

  async recordSuccess(providerId: string): Promise<void> {
    const state = await this.getCircuitState(providerId);
    
    if (state.state === 'HALF_OPEN') {
      // Success in half-open state, close the circuit
      await this.reset(providerId);
    } else if (state.state === 'OPEN') {
      // Check if timeout has passed
      const now = Date.now();
      if (now - state.lastFailureTime > state.timeout) {
        await this.setState(providerId, 'HALF_OPEN');
      }
    }
  }

  async recordError(providerId: string, error: any): Promise<void> {
    const state = await this.getCircuitState(providerId);
    
    if (state.state === 'CLOSED') {
      const newFailureCount = state.failureCount + 1;
      
      await this.redis.hset(
        `circuit:${providerId}`,
        {
          failureCount: newFailureCount,
          lastFailureTime: Date.now()
        }
      );

      if (newFailureCount >= state.threshold) {
        await this.setState(providerId, 'OPEN');
        this.emit('circuit:open', { providerId, error });
      }
    } else if (state.state === 'HALF_OPEN') {
      // Error in half-open state, open the circuit again
      await this.setState(providerId, 'OPEN');
      this.emit('circuit:open', { providerId, error });
    }
  }

  async reset(providerId: string): Promise<void> {
    await this.redis.hset(
      `circuit:${providerId}`,
      {
        state: 'CLOSED',
        failureCount: 0,
        lastFailureTime: 0
      }
    );
  }

  private async getCircuitState(providerId: string): Promise<{
    state: CircuitState;
    failureCount: number;
    lastFailureTime: number;
    threshold: number;
    timeout: number;
  }> {
    const data = await this.redis.hgetall(`circuit:${providerId}`);
    
    return {
      state: (data.state || 'CLOSED') as CircuitState,
      failureCount: parseInt(data.failureCount || '0'),
      lastFailureTime: parseInt(data.lastFailureTime || '0'),
      threshold: parseInt(data.threshold || this.defaultThreshold.toString()),
      timeout: parseInt(data.timeout || this.defaultTimeout.toString())
    };
  }

  private async setState(providerId: string, state: CircuitState): Promise<void> {
    await this.redis.hset(`circuit:${providerId}`, { state });
  }
}