import Redis from 'ioredis';
import { ProviderConfig } from './types';

export class ProviderRegistry {
  private redis: Redis;

  constructor(redis: Redis) {
    this.redis = redis;
  }

  async register(config: ProviderConfig): Promise<void> {
    const key = `provider:${config.id}`;
    await this.redis.hset(key, {
      ...config,
      models: JSON.stringify(config.models),
      capabilities: JSON.stringify(config.capabilities),
      rateLimits: JSON.stringify(config.rateLimits),
      pricing: JSON.stringify(config.pricing),
      health: JSON.stringify(config.health),
      registeredAt: new Date().toISOString()
    });
    
    await this.redis.sadd('providers:all', config.id);
  }

  async get(providerId: string): Promise<ProviderConfig | null> {
    const key = `provider:${providerId}`;
    const data = await this.redis.hgetall(key);
    
    if (!data || Object.keys(data).length === 0) {
      return null;
    }

    return {
      id: data.id,
      name: data.name,
      type: data.type as any,
      apiKey: data.apiKey,
      baseUrl: data.baseUrl,
      models: JSON.parse(data.models || '[]'),
      capabilities: JSON.parse(data.capabilities || '[]'),
      rateLimits: JSON.parse(data.rateLimits || '{}'),
      pricing: JSON.parse(data.pricing || '{}'),
      health: JSON.parse(data.health || '{}')
    };
  }

  async getAll(): Promise<ProviderConfig[]> {
    const providerIds = await this.redis.smembers('providers:all');
    const providers = await Promise.all(
      providerIds.map(id => this.get(id))
    );
    
    return providers.filter(p => p !== null) as ProviderConfig[];
  }

  async update(providerId: string, updates: Partial<ProviderConfig>): Promise<void> {
    const key = `provider:${providerId}`;
    const existing = await this.get(providerId);
    
    if (!existing) {
      throw new Error(`Provider ${providerId} not found`);
    }

    const updated = { ...existing, ...updates };
    await this.register(updated);
  }

  async remove(providerId: string): Promise<void> {
    const key = `provider:${providerId}`;
    await this.redis.del(key);
    await this.redis.srem('providers:all', providerId);
  }

  async exists(providerId: string): Promise<boolean> {
    return await this.redis.sismember('providers:all', providerId) === 1;
  }
}