import axios from 'axios';
import { BaseAdapter } from './base-adapter';
import { ProviderConfig, ModelConfig, AIRequest, AIResponse } from '../types';

export class OpenAIAdapter extends BaseAdapter {
  async execute(request: AIRequest, selection: { provider: ProviderConfig; model: ModelConfig }): Promise<AIResponse> {
    const { provider, model } = selection;
    
    const response = await axios.post(
      `${provider.baseUrl || 'https://api.openai.com/v1'}/chat/completions`,
      {
        model: model.id,
        messages: request.messages,
        max_tokens: request.maxTokens,
        temperature: request.temperature,
        tools: request.tools,
        stream: request.stream
      },
      {
        headers: {
          'Authorization': `Bearer ${provider.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    const data = response.data;
    
    return {
      id: data.id,
      provider: provider.id,
      model: model.id,
      choices: data.choices,
      usage: data.usage,
      cost: this.calculateCost(
        { input: data.usage.prompt_tokens, output: data.usage.completion_tokens },
        model.pricing
      ),
      timestamp: new Date()
    };
  }

  validateConfig(config: ProviderConfig): boolean {
    return !!(config.apiKey && config.type === 'openai');
  }

  getModels(config: ProviderConfig): ModelConfig[] {
    return [
      {
        id: 'gpt-4',
        name: 'GPT-4',
        contextWindow: 8192,
        maxTokens: 4096,
        supportsStreaming: true,
        supportsTools: true,
        supportsImages: false,
        pricing: { input: 0.03, output: 0.06 }
      },
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        contextWindow: 128000,
        maxTokens: 4096,
        supportsStreaming: true,
        supportsTools: true,
        supportsImages: true,
        pricing: { input: 0.01, output: 0.03 }
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        contextWindow: 4096,
        maxTokens: 4096,
        supportsStreaming: true,
        supportsTools: true,
        supportsImages: false,
        pricing: { input: 0.0015, output: 0.002 }
      }
    ];
  }
}